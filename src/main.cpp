#include <Arduino.h>
#include <WiFi.h>
#include <gpio_viewer.h>

// WiFi credentials - ЗАМЕНИТЕ НА ВАШИ!
const char* ssid = "Irma";
const char* password = "555378777";

// GPIO pins to test
const int BTN_PIN_5 = 10;   // Оригинальный пин, который не работал
const int BTN_PIN_48 = 48; // Альтернативный пин
const int LED_PIN = 2;     // Встроенный LED для индикации

// GPIOViewer instance
GPIOViewer gpioViewer;

void setup() {
  Serial.begin(115200);
  delay(1000);

  Serial.println("=== ESP32-S3 GPIO Monitor with GPIOViewer ===");

  // Configure GPIO pins
  Serial.println("Configuring GPIO pins...");
  pinMode(BTN_PIN_5, OUTPUT);
  pinMode(BTN_PIN_48, OUTPUT);
  pinMode(LED_PIN, OUTPUT);

  // Set initial states
  digitalWrite(BTN_PIN_5, LOW);
  digitalWrite(BTN_PIN_48, LOW);
  digitalWrite(LED_PIN, LOW);
  Serial.println("GPIO pins configured.");

  // Connect to WiFi using GPIOViewer method
  Serial.print("Connecting to WiFi network: ");
  Serial.println(ssid);
  gpioViewer.connectToWifi(ssid, password);

  Serial.print("WiFi connected! IP: ");
  Serial.println(WiFi.localIP());

  // Set sampling interval (optional)
  gpioViewer.setSamplingInterval(100); // 100ms

  // Initialize GPIOViewer
  Serial.println("Starting GPIOViewer...");
  gpioViewer.begin();

  Serial.println("Setup complete. Starting GPIO test...");
}

void loop() {
  static unsigned long lastToggle = 0;
  static bool state = false;

  // Toggle pins every 1 second
  if (millis() - lastToggle > 2000) {
    state = !state;

    Serial.print("Setting GPIO10=");
    Serial.print(state ? "HIGH" : "LOW");
    Serial.print(", GPIO48=");
    Serial.print(state ? "HIGH" : "LOW");
    Serial.print(", LED=");
    Serial.println(state ? "HIGH" : "LOW");

    digitalWrite(BTN_PIN_10, state);
    digitalWrite(BTN_PIN_48, state);
    digitalWrite(LED_PIN, state);

    lastToggle = millis();
  }

  // GPIOViewer работает в отдельной задаче, не нужно вызывать loop()
  delay(10);
}