#include <Arduino.h>
#include <WiFi.h>
#include <gpio_viewer.h>

// WiFi credentials - ЗАМЕНИТЕ НА ВАШИ!
const char* ssid = "Irma";
const char* password = "555378777";

// GPIO pins to test
const int MOSFET_GATE_PIN = 10;  // MOSFET Gate через резистор 220 Ом
const int BTN_PIN_48 = 48;       // Альтернативный пин
const int LED_PIN = 2;           // Встроенный LED для индикации

// Для тестирования MOSFET
const int DRAIN_READ_PIN = 11;   // Пин для чтения напряжения на Drain (если подключен)

// GPIOViewer instance
GPIOViewer gpioViewer;

void setup() {
  Serial.begin(115200);
  delay(1000);

  Serial.println("=== ESP32-S3 GPIO Monitor with GPIOViewer ===");

  // Configure GPIO pins
  Serial.println("Configuring GPIO pins...");
  pinMode(MOSFET_GATE_PIN, OUTPUT);
  pinMode(BTN_PIN_48, OUTPUT);
  pinMode(LED_PIN, OUTPUT);
  pinMode(DRAIN_READ_PIN, INPUT);  // Для чтения напряжения на drain

  // Set initial states
  digitalWrite(MOSFET_GATE_PIN, LOW);
  digitalWrite(BTN_PIN_48, LOW);
  digitalWrite(LED_PIN, LOW);
  Serial.println("GPIO pins configured.");
  Serial.println("MOSFET Gate connected to GPIO10 through 220R resistor");
  Serial.println("Gate has 10K pulldown to GND");

  // Connect to WiFi using GPIOViewer method
  Serial.print("Connecting to WiFi network: ");
  Serial.println(ssid);
  gpioViewer.connectToWifi(ssid, password);

  Serial.print("WiFi connected! IP: ");
  Serial.println(WiFi.localIP());

  // Set sampling interval (optional)
  gpioViewer.setSamplingInterval(100); // 100ms

  // Initialize GPIOViewer
  Serial.println("Starting GPIOViewer...");
  gpioViewer.begin();

  Serial.println("Setup complete. Starting GPIO test...");
}

void loop() {
  static unsigned long lastToggle = 0;
  static bool state = false;

  // Toggle pins every 2 seconds for better observation
  if (millis() - lastToggle > 2000) {
    state = !state;

    // Set MOSFET gate state
    digitalWrite(MOSFET_GATE_PIN, state);
    digitalWrite(BTN_PIN_48, state);
    digitalWrite(LED_PIN, state);

    // Read drain voltage (if connected to ADC pin)
    int drainReading = digitalRead(DRAIN_READ_PIN);

    Serial.println("=== MOSFET Test ===");
    Serial.print("Gate (GPIO10): ");
    Serial.print(state ? "HIGH (3.3V)" : "LOW (0V)");
    Serial.print(" | Drain (GPIO11): ");
    Serial.print(drainReading ? "HIGH" : "LOW");
    Serial.print(" | LED: ");
    Serial.println(state ? "ON" : "OFF");

    // Voltage measurements info
    Serial.println("Expected behavior:");
    if (state) {
      Serial.println("- Gate HIGH -> MOSFET ON -> Drain should be LOW (if pullup present)");
    } else {
      Serial.println("- Gate LOW -> MOSFET OFF -> Drain floating (or HIGH if pullup present)");
    }
    Serial.println("Note: Connect LED+resistor between Drain and 3.3V to see switching");
    Serial.println();

    lastToggle = millis();
  }

  // GPIOViewer работает в отдельной задаче, не нужно вызывать loop()
  delay(10);
}