#include <Arduino.h>
#include <WiFi.h>
#include <gpio_viewer.h>

// WiFi credentials - ЗАМЕНИТЕ НА ВАШИ!
const char* ssid = "Irma";
const char* password = "555378777";

// GPIO pins to test
const int MOSFET_GATE_PIN = 10;  // MOSFET Gate через резистор 220 Ом
const int BTN_PIN_48 = 48;       // Альтернативный пин
const int LED_PIN = 2;           // Встроенный LED для индикации

// Для тестирования MOSFET
const int DRAIN_READ_PIN = 11;   // Пин для чтения напряжения на Drain (если подключен)

// GPIOViewer instance
GPIOViewer gpioViewer;

// Function to simulate button press
void pressButton(int durationMs = 100) {
  Serial.print("Pressing button for ");
  Serial.print(durationMs);
  Serial.println("ms...");

  digitalWrite(MOSFET_GATE_PIN, HIGH);  // Press
  delay(durationMs);
  digitalWrite(MOSFET_GATE_PIN, LOW);   // Release

  Serial.println("Button released.");
}

void setup() {
  Serial.begin(115200);
  delay(1000);

  Serial.println("=== ESP32-S3 GPIO Monitor with GPIOViewer ===");

  // Configure GPIO pins
  Serial.println("Configuring GPIO pins...");
  pinMode(MOSFET_GATE_PIN, OUTPUT);
  pinMode(BTN_PIN_48, OUTPUT);
  pinMode(LED_PIN, OUTPUT);
  pinMode(DRAIN_READ_PIN, INPUT);  // Для чтения напряжения на drain

  // Set initial states
  digitalWrite(MOSFET_GATE_PIN, LOW);
  digitalWrite(BTN_PIN_48, LOW);
  digitalWrite(LED_PIN, LOW);
  Serial.println("GPIO pins configured.");
  Serial.println("MOSFET Gate connected to GPIO10 through 220R resistor");
  Serial.println("Gate has 10K pulldown to GND");

  // Connect to WiFi using GPIOViewer method
  Serial.print("Connecting to WiFi network: ");
  Serial.println(ssid);
  gpioViewer.connectToWifi(ssid, password);

  Serial.print("WiFi connected! IP: ");
  Serial.println(WiFi.localIP());

  // Set sampling interval (optional)
  gpioViewer.setSamplingInterval(100); // 100ms

  // Initialize GPIOViewer
  Serial.println("Starting GPIOViewer...");
  gpioViewer.begin();

  Serial.println("Setup complete. Starting GPIO test...");
}

void loop() {
  static unsigned long lastAction = 0;
  static int testMode = 0;

  // Different test modes every 5 seconds
  if (millis() - lastAction > 5000) {

    switch(testMode) {
      case 0:
        Serial.println("\n=== Test 1: Short button press (100ms) ===");
        pressButton(100);
        break;

      case 1:
        Serial.println("\n=== Test 2: Long button press (500ms) ===");
        pressButton(500);
        break;

      case 2:
        Serial.println("\n=== Test 3: Double click ===");
        pressButton(50);
        delay(100);
        pressButton(50);
        break;

      case 3:
        Serial.println("\n=== Test 4: Manual toggle (2 sec hold) ===");
        digitalWrite(MOSFET_GATE_PIN, HIGH);
        digitalWrite(LED_PIN, HIGH);
        Serial.println("Button PRESSED (holding for 2 seconds)");
        delay(2000);
        digitalWrite(MOSFET_GATE_PIN, LOW);
        digitalWrite(LED_PIN, LOW);
        Serial.println("Button RELEASED");
        break;
    }

    testMode = (testMode + 1) % 4;
    lastAction = millis();

    // Status info
    Serial.println("\nCurrent MOSFET state:");
    Serial.print("Gate: ");
    Serial.println(digitalRead(MOSFET_GATE_PIN) ? "HIGH" : "LOW");
    if (DRAIN_READ_PIN != MOSFET_GATE_PIN) {
      Serial.print("Drain monitor: ");
      Serial.println(digitalRead(DRAIN_READ_PIN) ? "HIGH" : "LOW");
    }
    Serial.println("Check GPIOViewer at http://192.168.0.7:8080");
    Serial.println("---");
  }

  delay(10);
}