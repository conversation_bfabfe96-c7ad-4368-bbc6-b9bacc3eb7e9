# Быстрый старт - ESP32 RGB LED Controller

## Что нужно изменить перед компиляцией

### 1. Настройка WiFi в `include/config.h`

```cpp
// Замените на ваши данные WiFi
const char* WIFI_SSID = "Ваша_WiFi_Сеть";
const char* WIFI_PASSWORD = "Ваш_Пароль_WiFi";

// Замените на сложный пароль для OTA
#define OTA_PASSWORD "ваш_сложный_пароль_ota"
```

### 2. Проверка пина LED в `include/config.h`

```cpp
// Убедитесь что пин соответствует вашей плате
#define LED_PIN     48  // Для ESP32-S3 SuperMini
```

## Компиляция и загрузка

### Через PlatformIO (рекомендуется)

1. **Первая загрузка** (через USB):
   ```bash
   pio run --target upload
   ```

2. **Загрузка файловой системы** (через USB):
   ```bash
   pio run --target uploadfs
   ```

3. **Последующие загрузки** (через OTA):
   ```bash
   # Узнайте IP адрес ESP32 из Serial Monitor
   pio run --target upload --upload-port *************
   ```

### Через Arduino IDE

1. **Установите библиотеки**:
   - FastLED
   - ArduinoJson
   - ESP32 Core

2. **Настройте плату**:
   - Плата: ESP32S3 Dev Module
   - Partition Scheme: Default 4MB with spiffs

3. **Загрузите код**: Sketch → Upload

4. **Загрузите файлы**: Tools → ESP32 Sketch Data Upload

## После загрузки

### 1. Найдите IP адрес

Откройте Serial Monitor (115200 baud) и найдите строку:
```
Connected to WiFi. IP address: *************
```

### 2. Откройте веб-интерфейс

- HTTP: `http://*************`
- HTTPS: `https://*************:443`
- mDNS: `http://myesp1000.local`

### 3. Проверьте функции

- ✅ Управление RGB слайдерами
- ✅ Информация о системе
- ✅ OTA готовность
- ✅ HTTPS доступность

## Новые возможности

### LED эффекты

Добавлены новые API endpoints:

- `GET /get-effects` - список доступных эффектов
- `GET /set-effect?mode=1` - установка эффекта (0-4)

### Доступные эффекты:

- **0 - Static**: статический цвет (по умолчанию)
- **1 - Rainbow**: радужный переход
- **2 - Fade**: плавное затухание/появление
- **3 - Strobe**: стробоскоп
- **4 - Breathing**: эффект дыхания

### Примеры использования:

```bash
# Установить радужный эффект
curl "http://*************/set-effect?mode=1"

# Получить список эффектов
curl "http://*************/get-effects"

# Установить статический красный цвет
curl "http://*************/set-effect?mode=0"
curl "http://*************/update-led?r=255&g=0&b=0"
```

## Устранение проблем

### ESP32 не подключается к WiFi

1. Проверьте SSID и пароль в `config.h`
2. Убедитесь что сеть 2.4GHz (ESP32 не поддерживает 5GHz)
3. Проверьте Serial Monitor на ошибки

### OTA не работает

1. Убедитесь что ESP32 и компьютер в одной сети
2. Проверьте что firewall не блокирует порт 3232
3. Используйте IP адрес вместо mDNS имени

### Веб-интерфейс не загружается

1. Убедитесь что загружена файловая система (`uploadfs`)
2. Проверьте что файлы в папке `data/` присутствуют
3. Попробуйте обновить страницу

### LED не работает

1. Проверьте правильность пина в `config.h`
2. Убедитесь что LED подключен правильно
3. Проверьте питание LED

## Дальнейшая разработка

### Добавление нового эффекта

1. Откройте `include/led_effects.h`
2. Добавьте новый режим в `enum EffectMode`
3. Добавьте функцию эффекта
4. Обновите `update()` и `getModeName()`
5. Обновите `handleGetEffects()` в `main.cpp`

### Добавление нового API

1. Создайте функцию обработчик в `main.cpp`
2. Добавьте маршрут в `setup()`
3. Обновите веб-интерфейс при необходимости

Удачи в разработке! 🚀