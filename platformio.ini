[env:esp32-s3-devkitc-1]
platform = espressif32@^6.0.0
board = esp32-s3-devkitc-1
framework = arduino
lib_deps = 
  FastLED
  bblanchon/Arduino<PERSON>son @ ^6.21.3
board_upload.flash_size = 4MB
board_build.partitions = default.csv
build_flags = 
  -DARDUINO_USB_CDC_ON_BOOT=1
  -DBOARD_HAS_PSRAM
board_build.filesystem = littlefs
; upload_protocol = espota
; upload_port = 192.168.0.2
; upload_flags =
;     --port=3232
;     --auth=your_ota_password_here